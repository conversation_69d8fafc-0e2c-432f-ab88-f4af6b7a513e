'use client'

import { useEffect, useRef } from 'react'

const ThemeHolder = () => {
  const htlmRef = useRef<HTMLHtmlElement>(null)
  const prevTheme = useRef('')

  useEffect(() => {
    htlmRef.current = document.querySelector('html')
    if (htlmRef.current) {
      prevTheme.current = htlmRef.current.classList.contains('dark') ? 'dark' : 'light'
      htlmRef.current.classList.remove('dark')
    }

    return () => {
      if (htlmRef.current) {
        htlmRef.current.classList.add(prevTheme.current)
      }
    }
  }, [])

  return null
}

export default ThemeHolder
