'use client'

import IconButton from '@/components/IconButton'
import { Trash2 } from 'lucide-react'
import { MouseEvent, useState } from 'react'
import DeleteAlert from './DeleteAlert'
import { toast } from 'sonner'

type Props = {
  id: string
}

const DeleteButton = ({ id }: Props) => {
  const [isDeleteAlertOpen, setDeleteAlertOpen] = useState(false)

  const handleClickDelete = (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation()
    setDeleteAlertOpen(true)
  }

  const handleRemoveRecommendation = (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation()
    toast.success('Recommendation deleted: ' + id)
    // TODO: Add delete logic
  }

  return (
    <>
      <IconButton
        variant="card"
        Icon={Trash2}
        onClick={handleClickDelete}
        className="p-2 h-auto hover:text-red-600"
      />
      <DeleteAlert
        isOpen={isDeleteAlertOpen}
        setIsOpen={setDeleteAlertOpen}
        handleDeleteRecommendation={handleRemoveRecommendation}
      />
    </>
  )
}

export default DeleteButton
