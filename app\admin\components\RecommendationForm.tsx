'use client'

import { useMemo, useState } from 'react'
import { Button } from '@/components/UI/button'
import { Input } from '@/components/UI/input'
import { Textarea } from '@/components/UI/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/UI/select'
import { Plus, Save, MapPin, Clock, Tag, Wifi, LucideIcon } from 'lucide-react'
import { PriceLevel, Recommendation } from '@/types/types'
import {
  PRICE_LEVEL_MAP,
  RECOMMENDATION_CATEGORIES,
  COMMON_AMENITIES,
  COMMON_TAGS,
} from '@/data/recommendations'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/UI/form'
// import { AddressPicker } from './AddressPicker'
import { toast } from 'sonner'
import { z } from 'zod'
import { SubmitHandler, useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { cn } from '@/lib/utils'
import { useRecommendationForm } from '@/stores/recommendationForm.store'
import CheckboxCard from '@/components/CheckboxCard'
import { getRecommendationById } from '@/data/mockData'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/UI/dialog'
import IconButton from '@/components/IconButton'
import { ScrollArea } from '@/components/UI/scroll-area'

const recommendationSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(1, 'Short description is required'),
  rating: z.number().min(0).max(5),
  priceLevel: z.enum(Object.keys(PRICE_LEVEL_MAP), {
    message: 'Price level is required',
  }),
  category: z.enum(RECOMMENDATION_CATEGORIES, {
    message: 'Category is required',
  }),
  address: z.string().min(1, 'Address is required'),
  hours: z.string().min(1, 'Opening hours are required'),
  image: z.string().min(1, 'Image is required'),
  tags: z.array(z.string()),
  phone: z.string().optional(),
  website: z.string().optional(),
  detailedDescription: z.string().min(50, 'Detailed description is required (min 50 characters)'),
  amenities: z.array(z.string()),
  images: z.array(z.string()).optional(),
  coordinates: z.object({
    lat: z.number(),
    lng: z.number(),
  }),
  email: z.email(),
  social: z.object({
    instagram: z.string().optional(),
    facebook: z.string().optional(),
  }),
  googleMaps: z.string().optional(),
  menuHighlights: z.array(z.string()).optional(),
})

type RecommendationFormValues = z.infer<typeof recommendationSchema>

const defaultValues = {
  name: '',
  category: '',
  description: '',
  address: '',
  priceLevel: '',
  rating: 4.0,
  tags: [],
  amenities: [],
  hours: '',
  social: {
    instagram: '',
    facebook: '',
  },
  coordinates: { lat: 46.253, lng: 20.1414 },
  images: [],
  menuHighlights: [],
  detailedDescription: '',
  email: '',
  googleMaps: '',
  image: '',
  phone: '',
  website: '',
}

const RecommendationForm = () => {
  const recommendationId = useRecommendationForm(state => state.recommendationId)
  const clearRecommendationId = useRecommendationForm(state => state.clearRecommendationId)
  const recommendation = useMemo(() => {
    if (!recommendationId || recommendationId === 'new') return null
    return getRecommendationById(recommendationId)
  }, [recommendationId])
  const [isEditing] = useState(!!recommendation)
  const [newTag, setNewTag] = useState('')
  const [newAmenity, setNewAmenity] = useState('')
  const form = useForm<RecommendationFormValues>({
    resolver: zodResolver(recommendationSchema),
    defaultValues: !recommendation
      ? (defaultValues as RecommendationFormValues)
      : (recommendation as unknown as RecommendationFormValues),
  })
  const {
    handleSubmit,
    formState: { errors },
    control,
    reset,
  } = form

  const onSubmit: SubmitHandler<RecommendationFormValues> = data => {
    const newRecommendation: Recommendation = {
      ...data,
      id: recommendationId || `rec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      reviews: [],
      recommendedTravel: 'walking',
      travelTime: 0,
      priceLevel: Number(data.priceLevel) as PriceLevel,
      openingHours: {},
      googleMaps: data.googleMaps || '',
      images: data.images ? data.images : [],
      menuHighlights: data.menuHighlights ? data.menuHighlights : [],
    }

    // onSave(newRecommendation)
    toast.success('Recommendation saved!' + newRecommendation.name)
    clearRecommendationId()
  }

  const handleClose = () => {
    reset()
    clearRecommendationId()
  }

  return (
    <Dialog open={!!recommendationId} onOpenChange={isOpen => !isOpen && handleClose()}>
      <DialogContent className="w-full max-w-3xl mx-auto bg-white border border-gray-200 shadow-xl">
        <DialogHeader className="border-b border-gray-200 pb-4">
          <DialogTitle className="flex text-base font-normal items-center gap-3 text-gray-900">
            <div className="size-10 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center">
              {isEditing ? <Save size={20} color="white" /> : <Plus size={20} color="white" />}
            </div>
            {isEditing ? 'Edit Recommendation' : 'Add New Recommendation'}
          </DialogTitle>
        </DialogHeader>
        <ScrollArea className="h-[80dvh] pr-4 -mr-4">
          <Form {...form}>
            <form
              onSubmit={handleSubmit(onSubmit)}
              className="flex flex-col *:pb-8 gap-8 divide-y-[1px] divide-muted-foreground/40"
            >
              <FormSection title="Basic Information" icon={MapPin}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Name *</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Enter place name" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={control}
                    name="category"
                    render={({ field: { value, onChange } }) => (
                      <FormItem>
                        <FormLabel htmlFor="category">Category *</FormLabel>
                        <FormControl>
                          <Select onValueChange={onChange} defaultValue={value}>
                            <SelectTrigger
                              id="category"
                              className={cn('w-full', errors.category && 'border-red-500')}
                            >
                              <SelectValue placeholder="Select category" />
                            </SelectTrigger>
                            <SelectContent>
                              {RECOMMENDATION_CATEGORIES.map(cat => (
                                <SelectItem key={cat} value={cat}>
                                  {cat}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description *</FormLabel>
                      <FormControl>
                        <Textarea {...field} placeholder="Short description" className="min-h-24" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={control}
                  name="detailedDescription"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Detailed Description *</FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          placeholder="Detailed description"
                          className="min-h-24"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {/* TODO: Image upload */}
                <FormField
                  control={control}
                  name="image"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Image *</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter image URL" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {/* TODO: Image upload */}
                <FormField
                  control={control}
                  name="images"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Images</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter image URLs (comma separated)" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {/* TODO: Address Picker */}
                {/* <div className="space-y-2">
              <AddressPicker
                address={form.getValues('address') || ''}
                coordinates={form.getValues('coordinates') || { lat: 46.253, lng: 20.1414 }}
                onAddressChange={address => form.setValue('address', address)}
                onCoordinatesChange={coordinates => form.setValue('coordinates', coordinates)}
              />
            </div> */}
              </FormSection>
              <FormSection title="Details" icon={Clock}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={control}
                    name="priceLevel"
                    render={({ field: { onChange } }) => (
                      <FormItem>
                        <FormLabel htmlFor="priceLevel">Price Level *</FormLabel>
                        <FormControl>
                          <Select onValueChange={onChange}>
                            <SelectTrigger
                              id="priceLevel"
                              className={cn('w-full', errors.priceLevel && 'border-red-500')}
                            >
                              <SelectValue placeholder="Select price level" />
                            </SelectTrigger>
                            <SelectContent className="bg-white border-gray-200">
                              {Object.entries(PRICE_LEVEL_MAP).map(([key, value]) => (
                                <SelectItem key={key} value={key}>
                                  {value}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="rating"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Rating</FormLabel>
                        <FormControl>
                          <Input {...field} type="number" min="0" max="5" step="0.1" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* TODO: Make an actual time picker */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={control}
                    name="hours"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Hours *</FormLabel>
                        <FormControl>
                          <Input
                            type="tel"
                            {...field}
                            placeholder="e.g., Mon-Fri 9-18, Sat 10-16"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="+36 XX XXX XXXX" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={control}
                    name="website"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Website</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="https://example.com" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email *</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="<EMAIL>" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={control}
                    name="social.instagram"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Instagram</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="@username" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={control}
                    name="social.facebook"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Facebook</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="https://facebook.com/username" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </FormSection>
              <FormSection title="Tags" icon={Tag}>
                <FormField
                  control={control}
                  name="tags"
                  render={() => (
                    <FormItem className="flex-row flex-wrap">
                      {COMMON_TAGS.map(tag => (
                        <FormField
                          key={tag}
                          control={control}
                          name="tags"
                          render={({ field: { value, onChange } }) => (
                            <FormItem>
                              <FormControl>
                                <CheckboxCard
                                  name="tags"
                                  label={tag}
                                  size="sm"
                                  checked={value.includes(tag)}
                                  onCheckedChange={checked =>
                                    onChange(
                                      checked ? [...value, tag] : value.filter(l => l !== tag)
                                    )
                                  }
                                  gradient={'from-indigo-500 to-purple-600'}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      ))}
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="flex gap-2 items-center">
                  <Input
                    value={newTag}
                    onChange={e => setNewTag(e.target.value)}
                    placeholder="Add custom tag"
                    // onKeyPress={e => e.key === 'Enter' && (e.preventDefault(), addTag(
                  />
                  <Button
                    type="button"
                    // onClick={addTag}
                    size="sm"
                    variant="active"
                    className="rounded-lg"
                  >
                    <Plus className="size-4" />
                  </Button>
                </div>
              </FormSection>
              <FormSection title="Amenities" icon={Wifi}>
                <FormField
                  control={control}
                  name="amenities"
                  render={() => (
                    <FormItem className="flex-row flex-wrap">
                      {COMMON_AMENITIES.map(amenity => (
                        <FormField
                          key={amenity}
                          control={control}
                          name="amenities"
                          render={({ field: { value, onChange } }) => (
                            <FormItem>
                              <FormControl>
                                <CheckboxCard
                                  name="amenities"
                                  label={amenity}
                                  size="sm"
                                  checked={value.includes(amenity)}
                                  onCheckedChange={checked =>
                                    onChange(
                                      checked
                                        ? [...value, amenity]
                                        : value.filter(l => l !== amenity)
                                    )
                                  }
                                  gradient="from-emerald-500 to-teal-600"
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      ))}
                    </FormItem>
                  )}
                />
                <div className="flex gap-2 items-center">
                  <Input
                    value={newAmenity}
                    onChange={e => setNewAmenity(e.target.value)}
                    placeholder="Add custom amenity"
                    // onKeyPress={e => e.key === 'Enter' && (e.preventDefault(), addAmenity(
                  />
                  <Button
                    type="button"
                    // onClick={addAmenity}
                    size="sm"
                    variant="active"
                    className="rounded-lg"
                  >
                    <Plus className="size-4" />
                  </Button>
                </div>
              </FormSection>
              <div className="flex flex-col sm:flex-row gap-4 !py-0">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClose}
                  className="flex-1 !text-gray-700 !bg-white !border-gray-300 hover:!bg-gray-50 hover:!text-gray-900 rounded-md"
                >
                  Cancel
                </Button>
                <IconButton
                  Icon={Save}
                  text={isEditing ? 'Update Recommendation' : 'Create Recommendation'}
                  type="submit"
                  className="flex-1 bg-gradient-to-r from-indigo-500 to-purple-600 !text-white border-transparent hover:opacity-90 rounded-md"
                  iconSize={20}
                />
              </div>
            </form>
          </Form>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}

const FormSection = ({
  title,
  icon: Icon,
  className,
  children,
}: {
  title: string
  icon: LucideIcon
  children: React.ReactNode
  className?: string
}) => (
  <div className="flex flex-col gap-6">
    <h3 className="text-base flex items-center gap-2 font-semibold text-gray-900">
      <Icon size={20} color="currentColor" />
      {title}
    </h3>
    <div className={cn('flex flex-col gap-4', className)}>{children}</div>
  </div>
)

export default RecommendationForm
