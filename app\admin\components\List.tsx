'use client'

import { useMemo, useState } from 'react'
import <PERSON>Header from './ListHeader'
import FilterCard from './FilterCard'
import GridView from './GridView'
import { Recommendation } from '@/types/types'
import { RECOMMENDATION_CATEGORIES } from '@/data/recommendations'
import TableView from './TableView'
import { useDebounce } from '@uidotdev/usehooks'

type Props = {
  recommendations: Recommendation[]
}

const List = ({ recommendations }: Props) => {
  const [searchQuery, setSearchQuery] = useState('')
  const debouncedSearchQuery = useDebounce(searchQuery, 500)
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid')

  const filteredRecommendations = useMemo(
    () =>
      recommendations.filter(rec => {
        if (!rec) return false

        if (
          selectedCategory !== 'all' &&
          rec.category.toLowerCase() !== selectedCategory.toLowerCase()
        )
          return false

        return (
          rec.name.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
          rec.category.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
          rec.description.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
          rec.address.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
          (Array.isArray(rec.tags) &&
            rec.tags.some(
              tag => tag && tag.toLowerCase().includes(debouncedSearchQuery.toLowerCase())
            ))
        )
      }),
    [debouncedSearchQuery, recommendations, selectedCategory]
  )

  const stats = useMemo(
    () => ({
      total: recommendations.length,
      byCategory: RECOMMENDATION_CATEGORIES.reduce(
        (acc, cat) => {
          acc[cat] = recommendations.filter(rec => rec && rec.category === cat).length
          return acc
        },
        {} as Record<string, number>
      ),
    }),
    [recommendations]
  )

  return (
    <main className="relative max-w-screen-xl mx-auto lg:p-6 space-y-6 flex-1">
      <ListHeader stats={stats} numberOfFilteredResults={filteredRecommendations.length} />
      <FilterCard
        stats={stats}
        viewMode={viewMode}
        setViewMode={setViewMode}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        selectedCategory={selectedCategory}
        setSelectedCategory={setSelectedCategory}
      />
      {viewMode === 'grid' ? (
        <GridView recommendations={filteredRecommendations} />
      ) : (
        <TableView recommendations={filteredRecommendations} />
      )}
    </main>
  )
}
export default List
