'use client'

import React, { useEffect, useRef, useState, useCallback } from 'react'
import { Card } from '@/components/UI/card'
import { Button } from '@/components/UI/button'
import { Input } from '@/components/UI/input'
import { Label } from '@/components/UI/label'
import { Badge } from '@/components/UI/badge'
import { MapPin, Search, Crosshair, AlertCircle, RotateCcw, ExternalLink } from 'lucide-react'
import { MAPBOX_CONFIG, isMapboxConfigured } from '@/lib/mapConfig'
import { toast } from 'sonner'
import { useDebounce } from '@uidotdev/usehooks'
import { Map, Marker } from 'mapbox-gl'

type Coordinates = {
  lat: number
  lng: number
}

type Props = {
  address: string
  coordinates: Coordinates
  onAddressChange: (address: string) => void
  onCoordinatesChange: (coordinates: Coordinates) => void
  disabled?: boolean
}

type GeocodingResult = {
  place_name: string
  center: [number, number]
  context?: Array<{
    id: string
    text: string
  }>
}

export const AddressPicker = ({
  address,
  coordinates,
  onAddressChange,
  onCoordinatesChange,
  disabled = false,
}: Props) => {
  const mapContainer = useRef<HTMLDivElement>(null)
  const map = useRef<Map>(null)
  const marker = useRef<Marker>(null)
  const [mapLoaded, setMapLoaded] = useState(false)
  const [, setIsError] = useState(false)
  const [isSearching, setIsSearching] = useState(false)
  const [searchResults, setSearchResults] = useState<GeocodingResult[]>([])
  const [showResults, setShowResults] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const debouncedSearchTerm = useDebounce(searchTerm, 500)

  useEffect(() => {
    if (map.current || !mapContainer.current || !isMapboxConfigured()) {
      setIsError(true)
      return
    }

    const initializeMap = async () => {
      if (!isMapboxConfigured() || !mapContainer.current || !map.current) return
      try {
        const mapboxgl = await import('mapbox-gl')
        mapboxgl.default.accessToken = MAPBOX_CONFIG.accessToken

        map.current = new mapboxgl.default.Map({
          container: mapContainer.current!,
          style: MAPBOX_CONFIG.style,
          center: [coordinates.lng, coordinates.lat],
          zoom: 15,
          attributionControl: false,
        })

        map.current.on('load', () => setMapLoaded(true))
        map.current.on('error', () => setIsError(true))
        map.current.addControl(new mapboxgl.default.NavigationControl(), 'top-right')
      } catch (err) {
        console.error('Error loading Mapbox:', err)
        toast.error('Failed to load map')
      }
    }

    initializeMap()

    return () => {
      if (map.current) {
        map.current.remove()
        map.current = null
      }
      if (marker.current) {
        marker.current = null
      }
    }
  }, [coordinates.lat, coordinates.lng])

  useEffect(() => {
    if (map.current && mapLoaded && marker.current) {
      marker.current.setLngLat([coordinates.lng, coordinates.lat])
      map.current.setCenter([coordinates.lng, coordinates.lat])
    }
  }, [coordinates, mapLoaded])

  const updateLocation = useCallback(
    async (lat: number, lng: number) => {
      onCoordinatesChange({ lat, lng })

      // Reverse geocode to get address
      if (isMapboxConfigured()) {
        try {
          const response = await fetch(
            `https://api.mapbox.com/geocoding/v5/mapbox.places/${lng},${lat}.json?access_token=${MAPBOX_CONFIG.accessToken}&country=hu&limit=1`
          )
          const data = await response.json()

          if (data.features && data.features.length > 0) {
            const addressText = data.features[0].place_name
            onAddressChange(addressText)
            toast.success('Location updated')
          }
        } catch (err) {
          console.error('Reverse geocoding failed:', err)
        }
      }
    },
    [onCoordinatesChange, onAddressChange]
  )

  const addMarker = useCallback(() => {
    if (!map.current) return

    // Remove existing marker
    if (marker.current) {
      marker.current.remove()
      map.current._markers = []
    }

    // Create custom marker element
    const markerElement = document.createElement('div')
    markerElement.innerHTML = `
      <div class="relative">
        <div class="size-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full border-3 border-white shadow-xl flex items-center justify-center text-white cursor-pointer map-marker-inner transform transition-transform hover:scale-110">
          <svg class="size-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-indigo-500" />
        <div class="absolute inset-0 size-10 bg-indigo-400 rounded-full animate-ping opacity-30" />
      </div>
    `

    // Make marker draggable
    marker.current = new Marker({
      element: markerElement,
      draggable: !disabled,
    })
      .setLngLat([coordinates.lng, coordinates.lat])
      .addTo(map.current)

    // Handle drag end
    if (!disabled) {
      marker.current.on('dragend', () => {
        const { lng, lat } = marker.current!.getLngLat()
        updateLocation(lat, lng)
      })
    }
  }, [coordinates.lat, coordinates.lng, disabled, updateLocation])

  const searchAddresses = useCallback(async (query: string) => {
    if (!query.trim() || !isMapboxConfigured()) {
      setSearchResults([])
      setShowResults(false)
      return
    }

    setIsSearching(true)

    try {
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(query)}.json?access_token=${MAPBOX_CONFIG.accessToken}&country=hu&proximity=20.1414,46.2530&limit=5`
      )
      const data = await response.json()

      if (data.features) {
        setSearchResults(data.features)
        setShowResults(true)
      }
    } catch (err) {
      console.error('Geocoding failed:', err)
      toast.error('Address search failed')
    }

    setIsSearching(false)
  }, [])

  const selectSearchResult = async (result: GeocodingResult) => {
    const [lng, lat] = result.center
    onAddressChange(result.place_name)
    onCoordinatesChange({ lat, lng })
    setSearchTerm('')
    setShowResults(false)
    setSearchResults([])

    // Update map view
    if (map.current && mapLoaded) {
      map.current.flyTo({
        center: [lng, lat],
        zoom: 16,
        duration: 1000,
      })

      if (marker.current) {
        marker.current.setLngLat([lng, lat])
      }
    }

    toast.success('Address selected')
  }

  const resetToSzeged = () => {
    const szegedCoords = { lat: 46.253, lng: 20.1414 }
    onCoordinatesChange(szegedCoords)
    onAddressChange('Szeged, Csongrád-Csanád, Hungary')

    if (map.current && mapLoaded) {
      map.current.flyTo({
        center: [szegedCoords.lng, szegedCoords.lat],
        zoom: 13,
        duration: 1000,
      })

      if (marker.current) {
        marker.current.setLngLat([szegedCoords.lng, szegedCoords.lat])
      }
    }

    toast.success('Reset to Szeged center')
  }

  const getCurrentLocation = () => {
    if (!navigator.geolocation) {
      toast.error('Geolocation is not supported')
      return
    }

    navigator.geolocation.getCurrentPosition(
      async position => {
        const { latitude, longitude } = position.coords
        updateLocation(latitude, longitude)

        if (map.current && mapLoaded) {
          map.current.flyTo({
            center: [longitude, latitude],
            zoom: 16,
            duration: 1000,
          })

          if (marker.current) {
            marker.current.setLngLat([longitude, latitude])
          }
        }
      },
      () => {
        toast.error('Unable to get current location')
      }
    )
  }

  useEffect(() => {
    if (debouncedSearchTerm.length) {
      searchAddresses(debouncedSearchTerm)
    } else {
      setSearchResults([])
      setShowResults(false)
    }
  }, [debouncedSearchTerm, searchAddresses])

  useEffect(() => {
    if (!map.current || !mapLoaded) return
    map.current.on('load', () => {
      setMapLoaded(true)
      addMarker()

      // Add click handler to map
      map.current!.on('click', e => {
        const { lng, lat } = e.lngLat
        updateLocation(lat, lng)
      })
    })

    map.current.on('error', e => {
      console.error('Mapbox error:', e)
      toast.error('Map failed to load')
    })
  }, [addMarker, mapLoaded, updateLocation])

  // Render fallback when Mapbox is not configured
  if (!isMapboxConfigured()) {
    return (
      <div className="space-y-4 text-slate-900">
        <div className="space-y-2">
          <Label>Address *</Label>
          <Input
            value={address}
            onChange={e => onAddressChange(e.target.value)}
            placeholder="Enter full address"
            disabled={disabled}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>Latitude</Label>
            <Input
              type="number"
              step="0.000001"
              value={coordinates.lat}
              onChange={e =>
                onCoordinatesChange({ ...coordinates, lat: parseFloat(e.target.value) || 0 })
              }
              disabled={disabled}
            />
          </div>
          <div className="space-y-2">
            <Label>Longitude</Label>
            <Input
              type="number"
              step="0.000001"
              value={coordinates.lng}
              onChange={e =>
                onCoordinatesChange({ ...coordinates, lng: parseFloat(e.target.value) || 0 })
              }
              disabled={disabled}
            />
          </div>
        </div>

        <Card className="p-4 bg-white/85 backdrop-blur-xl border-[1px] !border-gray-300">
          <div className="flex items-start gap-3">
            <AlertCircle className="size-5 text-amber-500 flex-shrink-0 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-slate-900 mb-1">Interactive Map Available</p>
              <p className="text-xs text-slate-700 mb-3">
                Configure Mapbox to enable address search and interactive location picking.
              </p>
              <a
                href="https://account.mapbox.com/"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-1 text-xs text-cyan-400 hover:text-cyan-300 transition-colors"
              >
                <ExternalLink className="size-3" />
                Get free Mapbox token
              </a>
            </div>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-4 text-slate-900">
      {/* Address Search */}
      <div className="space-y-2 relative">
        <Label>Search Address</Label>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            value={searchTerm}
            // onChange={(e) => handleSearchChange(e.target.value)}
            placeholder="Search for an address in Szeged..."
            className="pl-10"
            disabled={disabled || isSearching}
            // onFocus={() => searchTerm && setShowResults(true)}
            // onBlur={() => setTimeout(() => setShowResults(false), 200)}
          />
          {isSearching && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin" />
            </div>
          )}
          {/* Search Results Dropdown */}
          {showResults && searchResults.length > 0 && (
            <div className="absolute top-full left-0 right-0 z-50 bg-white/85 backdrop-blur-xl border-[1px] border-white/60 rounded-lg shadow-xl max-h-60 overflow-y-auto mt-1">
              {searchResults.map((result, index) => (
                <button
                  key={index}
                  onClick={() => selectSearchResult(result)}
                  className="w-full text-left px-4 py-3 hover:bg-white/20 border-b-[1px] border-white/60 last:border-b-0 transition-all duration-200 hover:bg-opacity-50"
                  disabled={disabled}
                >
                  <div className="font-medium text-slate-900 text-sm">{result.place_name}</div>
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Current Address */}
      <div className="space-y-2">
        <Label>Selected Address *</Label>
        <Input
          value={address}
          onChange={e => onAddressChange(e.target.value)}
          placeholder="Address will appear here"
          disabled={disabled}
        />
      </div>

      {/* Map Preview */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <Label>Location Preview</Label>
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={getCurrentLocation}
              disabled={disabled}
              className="text-xs !border-gray-200 !text-gray-600 !bg-white hover:!bg-gray-50"
            >
              <Crosshair className="size-3 mr-1" />
              Current
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={resetToSzeged}
              disabled={disabled}
              className="text-xs !border-gray-200 !text-gray-600 !bg-white hover:!bg-gray-50"
            >
              <RotateCcw className="size-3 mr-1" />
              Reset
            </Button>
          </div>
        </div>

        <Card className="relative h-64 overflow-hidden bg-white/85 backdrop-blur-xl border-[1px] border-gray-200 shadow-lg">
          <div
            ref={mapContainer}
            className="size-full rounded-lg min-h-64"
            style={{
              visibility: mapLoaded ? 'visible' : 'hidden',
            }}
          />

          {/* Loading State */}
          {!mapLoaded && (
            <div className="absolute inset-0 flex items-center justify-center bg-blue-50">
              <div className="text-center">
                <div className="size-8 border-2 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-2" />
                <p className="text-sm text-slate-700">Loading map...</p>
              </div>
            </div>
          )}

          {/* Instructions Overlay */}
          {mapLoaded && !disabled && (
            <div className="absolute top-3 left-3 z-20">
              <Badge className="bg-white/90 backdrop-blur-sm text-slate-700 text-xs px-2 py-1 shadow-md">
                <MapPin className="size-3 mr-1" />
                Click map or drag marker
              </Badge>
            </div>
          )}

          {/* Coordinates Display Overlay */}
          {mapLoaded && (
            <div className="absolute bottom-3 right-3 z-20">
              <Badge className="bg-black/70 backdrop-blur-sm text-white text-xs px-2 py-1 font-mono">
                {coordinates.lat.toFixed(4)}, {coordinates.lng.toFixed(4)}
              </Badge>
            </div>
          )}
        </Card>
      </div>

      {/* Coordinates Display */}
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label className="text-xs text-slate-700">Latitude</Label>
          <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm font-mono text-slate-700">
            {coordinates.lat.toFixed(6)}
          </div>
        </div>
        <div className="space-y-2">
          <Label className="text-xs text-slate-700">Longitude</Label>
          <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm font-mono text-slate-700">
            {coordinates.lng.toFixed(6)}
          </div>
        </div>
      </div>
    </div>
  )
}

export default AddressPicker
