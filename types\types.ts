import { PRICE_LEVEL_MAP, RECOMMENDATION_CATEGORIES, TRAVEL_METHODS } from '@/data/recommendations'
import getScrollConfig from '@/lib/getHeaderScrollConfig'
import { LucideIcon } from 'lucide-react'

export type Recommendation = {
  id: string
  name: string
  description: string
  rating: number
  priceLevel: PriceLevel
  category: RecommendationCategory
  address: string
  hours: string
  image: string
  tags: string[]
  phone?: string
  website?: string
  detailedDescription: string
  amenities: string[]
  images: string[]
  reviews: {
    id: string
    author: string
    rating: number
    comment: string
    date: string
  }[]
  coordinates: {
    lat: number
    lng: number
  }
  email?: string
  social: {
    instagram?: string
    facebook?: string
  }
  googleMaps: string
  openingHours?: Record<string, string>
  menuHighlights: string[]
  recommendedTravel: TravelMethod
  travelTime: number
}

export const themes = ['light', 'dark'] as const

export type Theme = (typeof themes)[number]

export const categories = ['eat', 'drink', 'do'] as const

export type Category = (typeof categories)[number]

export type CategoryDetails = {
  id: Category
  name: string
  title: string
  description: string
  subtitle: string
  gradient: string
  bgGradient: string
  shadowColor: string
  icon: LucideIcon
  accentIcon: LucideIcon
  emoji: string
  iconColor: string
}

export const recommendationListTypes = ['grid', 'list'] as const

export type RecommendationListType = (typeof recommendationListTypes)[number]

export type ScrollConfig = ReturnType<typeof getScrollConfig>

export type TravelTimeCategory = {
  minutes: number
  text: string
}

export type PriceLevel = keyof typeof PRICE_LEVEL_MAP

export type RecommendationCategory = (typeof RECOMMENDATION_CATEGORIES)[number]

export type TravelMethod = (typeof TRAVEL_METHODS)[number]
