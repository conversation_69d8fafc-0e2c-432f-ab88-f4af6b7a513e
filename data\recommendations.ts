export const PRICE_LEVEL_MAP = {
  0: 'Free',
  1: 'Budget ($)',
  2: 'Moderate ($$)',
  3: 'Premium ($$$)',
} as const

export const TRAVEL_TIME_CATEGORIES = [
  { maxMinutes: 0, text: 'All' },
  { maxMinutes: 5, text: 'Very Close (≤5 min)' },
  { maxMinutes: 10, text: 'Close (≤10 min)' },
  { maxMinutes: 15, text: 'Moderate (≤15 min)' },
  { maxMinutes: Infinity, text: 'Further (>15 min)' },
] as const

export const RECOMMENDATION_CATEGORIES = [
  'Café',
  'Bakery',
  'Restaurant',
  'Bar',
  'Museum',
  'Park',
  'Gallery',
  'Shop',
  'Activity',
  'Tour',
] as const

export const COMMON_TAGS = [
  'cozy',
  'modern',
  'traditional',
  'family-friendly',
  'romantic',
  'budget',
  'luxury',
  'outdoor',
  'historic',
  'trendy',
] as const

export const COMMON_AMENITIES = [
  'WiFi',
  'Parking',
  'Terrace',
  'Card Payment',
  'English Menu',
  'Vegetarian Options',
  'Disabled Access',
  'Air Conditioning',
] as const

export const TRAVEL_METHODS = ['walking', 'biking', 'public_transport', 'car'] as const
